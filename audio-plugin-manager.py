# plugin_navigator_pro.py
#
# Plugin Navigator Pro v3.0
# Copyright (c) 2025 Develop Device Studio, Jaroslav Ho<PERSON>. All rights reserved.
#
# This software requires the following libraries: pip install customtkinter pillow
#
# An advanced tool for scanning, managing, and cataloging audio plugins.
# For more information, visit https://developdevice.com

import os
import json
import tkinter as tk
from tkinter import ttk, filedialog
import platform
import subprocess
import csv
import threading
from datetime import datetime
from collections import defaultdict
import customtkinter as ctk
from PIL import Image

# --- Configuration ---
Config = {
    'APP_NAME': "Plugin Navigator Pro",
    'APP_VERSION': "3.0",
    'APP_AUTHOR': "Develop Device Studio, Jaroslav Holub",
    'APP_YEAR': "2025",
    'WEBSITE_URL': "https://developdevice.com",
    'DATABASE_FILE': 'plugin_database.json',
    'PLUGIN_EXTENSIONS': ['.dll', '.vst3', '.component'],
    'DEFAULT_SCAN_FOLDERS': [
        os.path.expandvars(r'%ProgramFiles%\Steinberg\VstPlugins'),
        os.path.expandvars(r'%ProgramFiles%\Cakewalk\VstPlugins'),
        os.path.expandvars(r'%ProgramFiles(x86)%\Steinberg\VstPlugins'),
        os.path.expandvars(r'%ProgramFiles%\VSTPlugins'),
        os.path.expandvars(r'%ProgramFiles%\Native Instruments'),
        os.path.expandvars(r'%ProgramFiles(x86)%\VSTPlugins'),
        os.path.expandvars(r'%ProgramFiles%\Common Files\VST3'),
        os.path.expandvars(r'%ProgramFiles%\Common Files\VST2'),
        os.path.expandvars(r'%ProgramFiles(x86)%\Common Files\VST3'),
        '/Library/Audio/Plug-Ins/VST',
        '/Library/Audio/Plug-Ins/VST3',
        '/Library/Audio/Plug-Ins/Components',
    ],
    'EXCLUDED_FILES': {
        "webview2loader.dll",
        "microsoft.web.webview2.core.dll",
    }
}

# --- Core Logic ---

def get_plugin_bitness(file_path):
    if platform.system() != "Windows": return "N/A"
    try:
        with open(file_path, 'rb') as f:
            dos_header = f.read(64)
            if dos_header[0:2] != b'MZ': return 'Unknown'
            pe_offset = int.from_bytes(dos_header[60:64], 'little')
            f.seek(pe_offset)
            pe_header = f.read(6)
            if pe_header[0:2] != b'PE': return 'Unknown'
            machine_type = int.from_bytes(pe_header[4:6], 'little')
            return '64-bit' if machine_type == 0x8664 else '32-bit' if machine_type == 0x014c else 'Unknown'
    except Exception: return 'Unknown'

def get_win_file_details(filename):
    if platform.system() != "Windows" or not os.path.exists(filename): return None
    import ctypes
    import ctypes.wintypes
    try:
        size = ctypes.windll.version.GetFileVersionInfoSizeW(filename, None)
        if not size: return None
        res = ctypes.create_string_buffer(size)
        if not ctypes.windll.version.GetFileVersionInfoW(filename, 0, size, res): return None
        lp_buffer, u_len = ctypes.c_void_p(), ctypes.wintypes.UINT()
        if not ctypes.windll.version.VerQueryValueW(res, r"\\VarFileInfo\\Translation", ctypes.byref(lp_buffer), ctypes.byref(u_len)) or u_len.value < 4: return None
        codepage_info = ctypes.cast(lp_buffer.value, ctypes.POINTER(ctypes.c_ubyte * u_len.value)).contents
        lang, codepage = tuple(int.from_bytes(bytes(codepage_info[i:i+2]), 'little') for i in (0, 2))
        def query_value(name):
            query_path = f"\\StringFileInfo\\{lang:04x}{codepage:04x}\\{name}"
            if ctypes.windll.version.VerQueryValueW(res, query_path, ctypes.byref(lp_buffer), ctypes.byref(u_len)) and u_len.value > 0:
                return ctypes.wstring_at(lp_buffer.value, u_len.value-1)
            return None
        return {"ProductName": query_value("ProductName"), "CompanyName": query_value("CompanyName")}
    except Exception: return None

def scan_for_plugins(folders_to_scan):
    discovered_plugins = []
    for folder in folders_to_scan:
        if not os.path.exists(folder): continue
        for root, _, files in os.walk(folder):
            for file in files:
                if file.lower() in Config['EXCLUDED_FILES']: continue
                file_ext = os.path.splitext(file)[1].lower()
                if file_ext in Config['PLUGIN_EXTENSIONS']:
                    full_path = os.path.join(root, file)
                    plugin_name, developer, plugin_format, bitness = os.path.splitext(file)[0], 'N/A', 'Unknown', 'N/A'
                    if file_ext == '.dll':
                        plugin_format = 'VST2'
                        details = get_win_file_details(full_path)
                        if details:
                            plugin_name = details.get("ProductName") or plugin_name
                            developer = details.get("CompanyName") or 'Unknown'
                        bitness = get_plugin_bitness(full_path)
                    elif file_ext == '.vst3': plugin_format, bitness = 'VST3', '64-bit'
                    elif file_ext == '.component': plugin_format, bitness = 'AU', '64-bit'
                    date_modified = datetime.fromtimestamp(os.path.getmtime(full_path)).strftime('%Y-%m-%d')
                    discovered_plugins.append({'name': plugin_name, 'developer': developer, 'format': plugin_format, 'bitness': bitness, 'date_modified': date_modified, 'path': full_path, 'notes': '', 'tags': []})
    return discovered_plugins

def load_database():
    if not os.path.exists(Config['DATABASE_FILE']): return []
    with open(Config['DATABASE_FILE'], 'r', encoding='utf-8') as f:
        try:
            data = json.load(f)
            # Data migration: handle old format with 'bookmarked'
            for item in data:
                if 'bookmarked' in item:
                    if item['bookmarked'] and 'Favorite' not in item.get('tags', []):
                        if 'tags' not in item:
                            item['tags'] = []
                        item['tags'].append('Favorite')
                    del item['bookmarked']
            return data
        except json.JSONDecodeError:
            InfoDialog(title="Error", message="Could not read the database file. It might be corrupted.")
            return []

def save_database(db_data):
    with open(Config['DATABASE_FILE'], 'w', encoding='utf-8') as f:
        json.dump(db_data, f, indent=4)

# --- Modern UI Components ---

class InfoDialog(ctk.CTkToplevel):
    def __init__(self, title="Info", message=""):
        super().__init__()
        self.title(title)
        self.lift()
        self.attributes("-topmost", True)
        self.grab_set()
        self.geometry("450x180")
        self.resizable(False, False)
        self.protocol("WM_DELETE_WINDOW", self.destroy)
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(expand=True, fill="both", padx=20, pady=20)
        message_label = ctk.CTkLabel(main_frame, text=message, wraplength=400, justify="center")
        message_label.pack(expand=True, fill="both")
        ok_button = ctk.CTkButton(main_frame, text="OK", command=self.destroy, width=100)
        ok_button.pack(pady=(0, 10))
        self.after(50, self._center_window)

    def _center_window(self):
        self.update_idletasks()
        x = self.winfo_screenwidth() // 2 - self.winfo_width() // 2
        y = self.winfo_screenheight() // 2 - self.winfo_height() // 2
        self.geometry(f"+{x}+{y}")

class PluginNavigatorProApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title(f"{Config['APP_NAME']} v{Config['APP_VERSION']}")
        self.geometry("1366x768")
        self.minsize(1024, 600)
        self.plugin_database = load_database()
        self.build_ui()
        self.style_treeview()
        self.refresh_plugin_list()

    def build_ui(self):
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(2, weight=1)

        # --- Top Action Bar ---
        top_bar = ctk.CTkFrame(self, corner_radius=0)
        top_bar.grid(row=0, column=0, sticky="ew")
        ctk.CTkButton(top_bar, text="System Scan", command=self.start_scan_thread).pack(side="left", padx=10, pady=10)
        ctk.CTkButton(top_bar, text="Scan Folder...", command=lambda: self.start_scan_thread(manual=True)).pack(side="left", padx=(0, 10), pady=10)
        ctk.CTkButton(top_bar, text="Find Duplicates", command=self.find_duplicates).pack(side="left", padx=(0, 10), pady=10)
        ctk.CTkButton(top_bar, text="Export CSV", command=self.export_to_csv).pack(side="left", padx=(0, 10), pady=10)
        ctk.CTkButton(top_bar, text="About", command=self.show_about_dialog).pack(side="right", padx=10, pady=10)

        # --- Filter Bar ---
        filter_bar = ctk.CTkFrame(self)
        filter_bar.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        filter_bar.grid_columnconfigure(1, weight=1)
        filter_bar.grid_columnconfigure(3, weight=1)
        
        ctk.CTkLabel(filter_bar, text="Search:").grid(row=0, column=0, padx=(10, 5), pady=10)
        self.search_var = tk.StringVar()
        self.search_var.trace_add('write', lambda *args: self.refresh_plugin_list())
        ctk.CTkEntry(filter_bar, textvariable=self.search_var, placeholder_text="Enter name, developer...").grid(row=0, column=1, padx=(0, 20), pady=10, sticky="ew")

        ctk.CTkLabel(filter_bar, text="Filter by Tag:").grid(row=0, column=2, padx=(10, 5), pady=10)
        self.tag_filter_var = tk.StringVar()
        self.tag_filter_var.trace_add('write', lambda *args: self.refresh_plugin_list())
        ctk.CTkEntry(filter_bar, textvariable=self.tag_filter_var, placeholder_text="e.g., EQ, Reverb").grid(row=0, column=3, padx=(0, 20), pady=10, sticky="ew")

        ctk.CTkLabel(filter_bar, text="Format:").grid(row=0, column=4, padx=(10, 5), pady=10)
        self.format_var = ctk.StringVar(value="All")
        ctk.CTkOptionMenu(filter_bar, variable=self.format_var, values=['All', 'VST2', 'VST3', 'AU'], command=lambda e: self.refresh_plugin_list()).grid(row=0, column=5, padx=(0, 20), pady=10)

        ctk.CTkLabel(filter_bar, text="Architecture:").grid(row=0, column=6, padx=(10, 5), pady=10)
        self.bitness_var = ctk.StringVar(value="All")
        ctk.CTkOptionMenu(filter_bar, variable=self.bitness_var, values=['All', '32-bit', '64-bit', 'N/A'], command=lambda e: self.refresh_plugin_list()).grid(row=0, column=7, padx=(0, 20), pady=10)

        # --- Plugin List (Treeview) ---
        tree_frame = ctk.CTkFrame(self)
        tree_frame.grid(row=2, column=0, sticky="nsew", padx=10, pady=(0, 10))
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        columns = ('Name', 'Developer', 'Format', 'Bitness', 'Modified', 'Tags', 'Path')
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings')
        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_treeview_column(c, False))
        self.tree.column('Name', width=250)
        self.tree.column('Developer', width=200)
        self.tree.column('Format', width=80, anchor='center')
        self.tree.column('Bitness', width=90, anchor='center')
        self.tree.column('Modified', width=100, anchor='center')
        self.tree.column('Tags', width=150)
        self.tree.column('Path', width=450)
        self.tree.grid(row=0, column=0, sticky="nsew")
        scrollbar = ctk.CTkScrollbar(tree_frame, command=self.tree.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.tree.configure(yscrollcommand=scrollbar.set)
        self.tree.bind('<Double-1>', self.on_edit_plugin)
        self.tree.bind('<Button-3>', self.show_context_menu)

        # --- Status Bar ---
        self.status_frame = ctk.CTkFrame(self, corner_radius=0)
        self.status_frame.grid(row=3, column=0, sticky="ew", padx=0, pady=0)
        self.status_label = ctk.CTkLabel(self.status_frame, text="Ready.", anchor="w")
        self.status_label.pack(side="left", padx=10, pady=5)
        self.progress_bar = ctk.CTkProgressBar(self.status_frame, orientation='horizontal', mode='indeterminate')

    def style_treeview(self):
        style = ttk.Style()
        font_family = "Segoe UI" if platform.system() == "Windows" else "Calibri"
        font_size = 10
        bg_color = self._apply_appearance_mode(ctk.ThemeManager.theme["CTkFrame"]["fg_color"])
        text_color = self._apply_appearance_mode(ctk.ThemeManager.theme["CTkLabel"]["text_color"])
        header_color = self._apply_appearance_mode(ctk.ThemeManager.theme["CTkButton"]["fg_color"])
        
        # Define colors for selected state
        selected_bg_color = self._apply_appearance_mode(ctk.ThemeManager.theme["CTkButton"]["fg_color"])
        # To ensure readability, selected text should be high-contrast. Pure white works well on the blue selection background.
        selected_fg_color = "#FFFFFF"

        style.theme_use("default")
        style.configure("Treeview", background=bg_color, foreground=text_color, fieldbackground=bg_color, borderwidth=0, rowheight=28, font=(font_family, font_size))
        style.configure("Treeview.Heading", background=header_color, foreground=text_color, relief="flat", font=(font_family, font_size, 'bold'))
        style.map("Treeview.Heading", background=[('active', self._apply_appearance_mode(ctk.ThemeManager.theme["CTkButton"]["hover_color"]))])
        
        # FIX: Add foreground mapping for the 'selected' state to ensure high contrast.
        # This prevents issues like light-gray text on a blue background.
        style.map("Treeview",
                  background=[('selected', selected_bg_color)],
                  foreground=[('selected', selected_fg_color)])
                  
        # Keep original alternating row configuration
        self.tree.tag_configure('oddrow', background=self._apply_appearance_mode(ctk.ThemeManager.theme["CTk"]["fg_color"][0]))
        self.tree.tag_configure('evenrow', background=bg_color)

    def sort_treeview_column(self, column, reverse):
        items = [(self.tree.set(k, column), k) for k in self.tree.get_children('')]
        try: items.sort(key=lambda t: t[0].lower(), reverse=reverse)
        except (TypeError, AttributeError): items.sort(reverse=reverse)
        for index, (_, k) in enumerate(items): self.tree.move(k, '', index)
        self.tree.heading(column, command=lambda: self.sort_treeview_column(column, not reverse))

    def refresh_plugin_list(self, *args):
        search_term = self.search_var.get().lower()
        tag_filter = self.tag_filter_var.get().lower()
        target_format = self.format_var.get()
        target_bitness = self.bitness_var.get()
        self.tree.delete(*self.tree.get_children())
        count = 0
        for idx, plugin in enumerate(self.plugin_database):
            if target_format != 'All' and plugin.get('format', '') != target_format: continue
            if target_bitness != 'All' and plugin.get('bitness', '') != target_bitness: continue
            if search_term and not any(search_term in str(plugin.get(field, '')).lower() for field in ['name', 'developer', 'path', 'notes']): continue
            tags = plugin.get('tags', [])
            if tag_filter and not any(tag_filter in tag.lower() for tag in tags): continue
            
            tag_text = ', '.join(tags)
            row_tag = 'evenrow' if count % 2 == 0 else 'oddrow'
            self.tree.insert('', 'end', iid=str(idx), values=(plugin.get('name', ''), plugin.get('developer', ''), plugin.get('format', ''), plugin.get('bitness', ''), plugin.get('date_modified', ''), tag_text, plugin.get('path', '')), tags=(row_tag,))
            count += 1
        self.status_label.configure(text=f"Displaying {count} of {len(self.plugin_database)} plugins.")

    def start_scan_thread(self, manual=False):
        scan_folders = Config['DEFAULT_SCAN_FOLDERS']
        if manual:
            folder = filedialog.askdirectory(title="Select Folder to Scan")
            if not folder: return
            scan_folders = [folder]
        self.status_label.configure(text="Scanning...")
        self.progress_bar.pack(side="left", padx=10, pady=5, fill="x", expand=True)
        self.progress_bar.start()
        thread = threading.Thread(target=self._perform_scan, args=(scan_folders,), daemon=True)
        thread.start()

    def _perform_scan(self, scan_folders):
        found_plugins = scan_for_plugins(scan_folders)
        self.after(0, self._on_scan_complete, found_plugins)

    def _on_scan_complete(self, found_plugins):
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
        existing_paths = {p['path'] for p in self.plugin_database}
        new_plugins_count = 0
        for plugin in found_plugins:
            if plugin['path'] not in existing_paths:
                self.plugin_database.append(plugin)
                new_plugins_count += 1
        save_database(self.plugin_database)
        self.refresh_plugin_list()
        InfoDialog(title="Scan Complete", message=f"Scan finished. Added {new_plugins_count} new plugins.")

    def on_edit_plugin(self, event=None):
        selected_iid = self.tree.focus()
        if not selected_iid: return
        dialog = PluginPropertiesWindow(self, self.plugin_database[int(selected_iid)])
        self.wait_window(dialog)
        if dialog.was_updated:
            save_database(self.plugin_database)
            self.refresh_plugin_list()

    def show_context_menu(self, event):
        iid = self.tree.identify_row(event.y)
        if not iid: return
        self.tree.selection_set(iid)
        menu = tk.Menu(self, tearoff=0, bg="#2B2B2B", fg="white", activebackground="#555555", activeforeground="white", relief="flat")
        menu.add_command(label="Edit Plugin Properties", command=self.on_edit_plugin)
        menu.add_separator()
        menu.add_command(label="Open Containing Folder", command=lambda: self.open_plugin_location(self.plugin_database[int(iid)]['path']))
        menu.post(event.x_root, event.y_root)

    def open_plugin_location(self, path):
        folder = os.path.dirname(path)
        try:
            if platform.system() == "Windows": os.startfile(folder)
            elif platform.system() == "Darwin": subprocess.run(["open", folder])
            else: subprocess.run(["xdg-open", folder])
        except Exception as e:
            InfoDialog(title="Error", message=f"Could not open folder: {e}")

    def find_duplicates(self):
        duplicates = defaultdict(list)
        for plugin in self.plugin_database:
            duplicates[plugin['name'].lower()].append(plugin['path'])
        
        found = {name: paths for name, paths in duplicates.items() if len(paths) > 1}
        DuplicateFinderWindow(self, found)

    def export_to_csv(self):
        filepath = filedialog.asksaveasfilename(defaultextension=".csv", filetypes=[("CSV Files", "*.csv")], title="Export Database to CSV", initialfile="plugin_navigator_pro_export.csv")
        if not filepath: return
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                headers = ['Name', 'Developer', 'Format', 'Bitness', 'Tags', 'Date Modified', 'Notes', 'Path']
                writer.writerow(headers)
                for plugin in self.plugin_database:
                    writer.writerow([plugin.get('name', ''), plugin.get('developer', ''), plugin.get('format', ''), plugin.get('bitness', ''), ', '.join(plugin.get('tags', [])), plugin.get('date_modified', ''), plugin.get('notes', ''), plugin.get('path', '')])
            InfoDialog(title="Export Complete", message=f"Database successfully exported to:\n{filepath}")
        except Exception as e:
            InfoDialog(title="Export Error", message=f"An error occurred during export: {e}")

    def show_about_dialog(self):
        about_text = (f"{Config['APP_NAME']} v{Config['APP_VERSION']}\n\n"
                      f"© {Config['APP_YEAR']} {Config['APP_AUTHOR']}.\n"
                      "All rights reserved.\n\n"
                      "An advanced tool for managing and cataloging audio plugins.\n\n"
                      f"For more information, visit:\n{Config['WEBSITE_URL']}")
        InfoDialog(title=f"About {Config['APP_NAME']}", message=about_text)

class PluginPropertiesWindow(ctk.CTkToplevel):
    def __init__(self, parent, plugin):
        super().__init__(parent)
        self.plugin = plugin
        self.was_updated = False
        self.title("Edit Plugin Properties")
        self.lift()
        self.attributes("-topmost", True)
        self.transient(parent)
        self.grab_set()
        self.resizable(False, False)
        self.build_widgets()
        self.after(50, self._center_window)

    def _center_window(self):
        self.update_idletasks()
        x = self.master.winfo_x() + self.master.winfo_width() // 2 - self.winfo_width() // 2
        y = self.master.winfo_y() + self.master.winfo_height() // 2 - self.winfo_height() // 2
        self.geometry(f"+{x}+{y}")

    def build_widgets(self):
        frame = ctk.CTkFrame(self)
        frame.pack(fill='both', expand=True, padx=20, pady=20)
        frame.grid_columnconfigure(1, weight=1)
        fields = [("Name", 'name'), ("Developer", 'developer'), ("Format", 'format'), ("Architecture", 'bitness')]
        self.vars = {}
        for i, (label, key) in enumerate(fields):
            ctk.CTkLabel(frame, text=f"{label}:").grid(row=i, column=0, sticky='w', padx=5, pady=8)
            var = tk.StringVar(value=self.plugin.get(key, ''))
            self.vars[key] = var
            ctk.CTkEntry(frame, textvariable=var, width=400).grid(row=i, column=1, sticky='ew', padx=5, pady=8)
        
        ctk.CTkLabel(frame, text="Tags:").grid(row=len(fields), column=0, sticky='w', padx=5, pady=8)
        self.tags_var = tk.StringVar(value=", ".join(self.plugin.get('tags', [])))
        ctk.CTkEntry(frame, textvariable=self.tags_var, placeholder_text="Comma-separated, e.g., EQ, Reverb").grid(row=len(fields), column=1, sticky='ew', padx=5, pady=8)

        ctk.CTkLabel(frame, text="Notes:").grid(row=len(fields)+1, column=0, sticky='nw', padx=5, pady=8)
        self.notes_text = ctk.CTkTextbox(frame, height=120)
        self.notes_text.grid(row=len(fields)+1, column=1, sticky='ew', padx=5, pady=8)
        self.notes_text.insert('1.0', self.plugin.get('notes', ''))
        
        btn_frame = ctk.CTkFrame(frame, fg_color="transparent")
        btn_frame.grid(row=len(fields)+2, column=0, columnspan=2, sticky='e', pady=(15, 0))
        ctk.CTkButton(btn_frame, text="Save", command=self.save_changes).pack(side='left', padx=5)
        ctk.CTkButton(btn_frame, text="Cancel", fg_color="gray50", hover_color="gray40", command=self.destroy).pack(side='left')

    def save_changes(self):
        for key, var in self.vars.items(): self.plugin[key] = var.get()
        self.plugin['notes'] = self.notes_text.get('1.0', 'end-1c')
        self.plugin['tags'] = [tag.strip() for tag in self.tags_var.get().split(',') if tag.strip()]
        self.was_updated = True
        self.destroy()

class DuplicateFinderWindow(ctk.CTkToplevel):
    def __init__(self, parent, duplicates):
        super().__init__(parent)
        self.title("Duplicate Plugin Finder")
        self.geometry("800x600")
        self.lift()
        self.attributes("-topmost", True)
        self.transient(parent)
        self.grab_set()

        self.textbox = ctk.CTkTextbox(self, wrap="word", font=("Segoe UI", 12))
        self.textbox.pack(expand=True, fill="both", padx=10, pady=10)

        if not duplicates:
            self.textbox.insert("1.0", "No duplicate plugins found.")
        else:
            report = "Duplicate Plugins Found:\n\n"
            for name, paths in duplicates.items():
                report += f"Plugin: {name.title()}\n"
                for path in paths:
                    report += f"  - Location: {path}\n"
                report += "\n"
            self.textbox.insert("1.0", report)
        
        self.textbox.configure(state="disabled")

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = PluginNavigatorProApp()
    app.mainloop()
